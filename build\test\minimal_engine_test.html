<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Engine Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #2a2a2a;
            color: #e0e0e0;
            margin: 0;
            padding: 20px;
        }
        #canvas {
            display: block;
            margin: 20px auto;
            border: 2px solid #555;
            background: #000;
        }
        .status {
            text-align: center;
            margin: 20px;
            padding: 10px;
            background: #3a3a3a;
            border-radius: 5px;
        }
        .error { background: #5a2a2a; }
        .success { background: #2a5a2a; }
        .loading { background: #5a5a2a; }
        #log {
            background: #1a1a1a;
            padding: 10px;
            margin: 20px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>🔬 Minimal Engine Test</h1>
    <p>This test loads the Godot engine with minimal configuration to isolate issues.</p>
    
    <div class="status" id="status">Ready to test</div>
    
    <button onclick="clearCacheAndReload()">Clear Cache & Reload</button>
    <button onclick="testEngineLoad()">Test Engine Load</button>
    <button onclick="clearLog()">Clear Log</button>
    
    <canvas id="canvas" width="800" height="600"></canvas>
    
    <div id="log"></div>

    <script>
        const EXECUTABLE_NAME = 'stiletto';
        const MAIN_PACK = 'stiletto.pck';
        
        let logDiv = document.getElementById('log');
        let statusDiv = document.getElementById('status');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            logDiv.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type}] ${message}`);
        }
        
        function setStatus(message, type = 'loading') {
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function clearLog() {
            logDiv.textContent = '';
        }
        
        function clearCacheAndReload() {
            log('Clearing cache and reloading page...', 'info');
            // Clear various caches
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => {
                        caches.delete(name);
                    });
                });
            }
            // Force reload without cache
            window.location.reload(true);
        }
        
        async function testEngineLoad() {
            clearLog();
            setStatus('Testing engine load...', 'loading');
            log('Starting minimal engine test');
            
            try {
                // Test file accessibility first
                log('Testing file access...');
                const files = ['stiletto.js', 'stiletto.wasm', 'stiletto.pck'];
                for (const file of files) {
                    try {
                        const response = await fetch(file, { method: 'HEAD' });
                        if (response.ok) {
                            log(`✓ ${file} accessible (${response.headers.get('content-length')} bytes)`);
                        } else {
                            log(`✗ ${file} not accessible (HTTP ${response.status})`, 'error');
                            return;
                        }
                    } catch (error) {
                        log(`✗ ${file} fetch error: ${error.message}`, 'error');
                        return;
                    }
                }
                
                // Load the engine script
                log('Loading engine script...');
                if (typeof Engine === 'undefined') {
                    await loadScript('stiletto.js');
                }
                
                if (typeof Engine === 'undefined') {
                    log('Engine class not available after loading script', 'error');
                    setStatus('Engine load failed', 'error');
                    return;
                }
                
                log('Engine class loaded successfully', 'success');
                
                // Test engine initialization
                log('Testing engine initialization...');
                const canvas = document.getElementById('canvas');
                
                const engine = new Engine({
                    canvas: canvas,
                    executable: EXECUTABLE_NAME,
                    mainPack: MAIN_PACK,
                    canvasResizePolicy: 0,
                    onProgress: function(current, total) {
                        if (total > 0) {
                            const percent = Math.round((current / total) * 100);
                            log(`Loading progress: ${percent}% (${current}/${total})`);
                            setStatus(`Loading: ${percent}%`, 'loading');
                        } else {
                            log(`Loading: ${current} bytes`);
                        }
                    },
                    onExit: function(code) {
                        log(`Engine exited with code: ${code}`, code === 0 ? 'success' : 'error');
                    },
                    onExecute: function(path, args) {
                        log(`Executing: ${path} ${args.join(' ')}`);
                    },
                    onPrint: function(text) {
                        log(`Engine: ${text}`);
                    },
                    onPrintError: function(text) {
                        log(`Engine Error: ${text}`, 'error');
                    }
                });
                
                log('Engine instance created, starting initialization...');
                setStatus('Initializing engine...', 'loading');
                
                // Start the engine
                await engine.startGame();
                
                log('Engine started successfully!', 'success');
                setStatus('Engine running', 'success');
                
            } catch (error) {
                log(`Engine test failed: ${error.message}`, 'error');
                log(`Stack trace: ${error.stack}`, 'error');
                setStatus('Engine test failed', 'error');
            }
        }
        
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }
        
        // Auto-run compatibility check on load
        window.addEventListener('load', () => {
            log('Page loaded, ready for testing');
            setStatus('Ready - Click "Test Engine Load" to begin', 'info');
        });
        
        // Capture and log any unhandled errors
        window.addEventListener('error', (event) => {
            log(`Unhandled error: ${event.error.message}`, 'error');
            log(`At: ${event.filename}:${event.lineno}:${event.colno}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            log(`Unhandled promise rejection: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>
