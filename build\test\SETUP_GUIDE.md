# 🌐 Web Export Setup Guide

This guide will help you set up web exports for remote device access.

## 📋 Prerequisites

1. **Godot Engine 4.4** installed at: `D:\Steam\steamapps\common\Godot Engine\`
2. **Web Export Templates** installed
3. **Python** for running the test server

## 🚀 Quick Setup

### Step 1: Install Web Export Templates
```bash
# Run this first to check/install templates:
build/test/install_web_template.bat
```

**OR manually in Godot:**
1. Open Godot Editor
2. Go to `Editor > Manage Export Templates`
3. Click `Download and Install`
4. Wait for download to complete
5. Close Godot

### Step 2: Export with Compatible Settings
```bash
# Export the project with threading disabled:
build/test/export_compatible.bat
```

### Step 3: Start Remote Server
```bash
# Start server that allows remote access:
build/test/start_remote_server.bat
```

### Step 4: Test on Remote Devices
1. Note the IP address shown in server output (e.g., `*************`)
2. On remote device, visit: `http://YOUR_IP:8000/compatibility_check.html`
3. If compatible, visit: `http://YOUR_IP:8000/stiletto.html`

## 🔧 Troubleshooting

### Export Template Missing
**Error:** `'godot' is not recognized` or `Export failed!`
**Solution:** Run `install_web_template.bat` or install templates manually

### SharedArrayBuffer Error
**Error:** `SharedArrayBuffer is not defined`
**Solution:** Export settings have been updated to disable threading

### Remote Access Issues
**Error:** Can't connect from other devices
**Solution:** 
- Check firewall allows port 8000
- Use IP address, not localhost
- Try `compatibility_check.html` first

### Browser Compatibility
**Error:** Game won't load on certain browsers
**Solution:** 
- Test with `compatibility_check.html`
- Try Chrome, Firefox, or Edge
- Ensure browser is up to date

## 📁 Files Created

- `export_compatible.bat` - Export with compatible settings
- `install_web_template.bat` - Check/install export templates  
- `start_remote_server.bat` - Start server for remote access
- `compatibility_check.html` - Test browser compatibility
- `remote_test.html` - Diagnostic tools for remote access

## ⚙️ Technical Details

### Export Settings Changed
- `variant/thread_support=false` - Disabled for compatibility
- `progressive_web_app/ensure_cross_origin_isolation_headers=false` - Removed SharedArrayBuffer requirement

### Server Configuration
- Binds to `0.0.0.0:8000` for remote access
- Removes Cross-Origin isolation headers
- Adds proper MIME types for Godot files

## 🎯 Expected Results

✅ **Local Access:** `http://localhost:8000/stiletto.html`
✅ **Remote Access:** `http://YOUR_IP:8000/stiletto.html`
✅ **No SharedArrayBuffer errors**
✅ **Works on more browsers and devices**
⚠️ **Slightly reduced performance** (no threading)

## 📞 Need Help?

1. Run `compatibility_check.html` to diagnose browser issues
2. Check server console for connection logs
3. Ensure all files are present: `stiletto.js`, `stiletto.wasm`, `stiletto.pck`
4. Verify firewall allows port 8000 connections
