@echo off
echo ========================================
echo  Installing Godot Web Export Template
echo ========================================
echo.
echo This will download and install the Web export template
echo for Godot 4.4 if it's not already installed.
echo.
echo Make sure you have an internet connection.
echo.
pause

echo Checking for existing templates...
echo.

REM Try to export to see if templates are available
"D:\Steam\steamapps\common\Godot Engine\godot.windows.opt.tools.64.exe" --headless --export-debug "Web" "build/test/template_test.html" 2>template_check.log

if %ERRORLEVEL% EQU 0 (
    echo ✅ Web export template is already installed!
    echo.
    echo You can now run: export_compatible.bat
    del template_check.log 2>nul
    del "build/test/template_test.html" 2>nul
) else (
    echo ❌ Web export template not found.
    echo.
    echo To install the Web export template:
    echo.
    echo 1. Open Godot Editor
    echo 2. Go to Editor ^> Manage Export Templates
    echo 3. Click "Download and Install"
    echo 4. Wait for download to complete
    echo 5. Close Godot and run export_compatible.bat
    echo.
    echo Alternative: Download manually from:
    echo https://godotengine.org/download/
    echo ^(Look for "Export templates" section^)
    echo.
    type template_check.log
    del template_check.log 2>nul
)

echo.
pause
