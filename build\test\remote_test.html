<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Remote Access Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #2a2a2a;
            color: #e0e0e0;
        }
        .test-section {
            background: #3a3a3a;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007acc;
        }
        .success { border-left-color: #28a745; }
        .error { border-left-color: #dc3545; }
        .warning { border-left-color: #ffc107; }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        #results { margin-top: 20px; }
        .file-test { margin: 5px 0; }
    </style>
</head>
<body>
    <h1>🌐 Remote Access Diagnostic Test</h1>
    
    <div class="test-section">
        <h3>Connection Info</h3>
        <p><strong>Your IP:</strong> <span id="client-ip">Detecting...</span></p>
        <p><strong>Server URL:</strong> <span id="server-url"></span></p>
        <p><strong>User Agent:</strong> <span id="user-agent"></span></p>
    </div>

    <div class="test-section">
        <h3>File Access Tests</h3>
        <button onclick="testFiles()">Test File Access</button>
        <div id="file-results"></div>
    </div>

    <div class="test-section">
        <h3>Godot Engine Tests</h3>
        <button onclick="testEngine()">Test Engine Loading</button>
        <div id="engine-results"></div>
    </div>

    <script>
        // Display basic info
        document.getElementById('server-url').textContent = window.location.origin;
        document.getElementById('user-agent').textContent = navigator.userAgent;

        // Get client IP
        fetch('/check_files.html')
            .then(() => {
                document.getElementById('client-ip').textContent = 'Connected successfully';
            })
            .catch(() => {
                document.getElementById('client-ip').textContent = 'Connection failed';
            });

        async function testFiles() {
            const files = [
                'stiletto.js',
                'stiletto.wasm', 
                'stiletto.pck',
                'stiletto.side.wasm'
            ];
            
            const resultsDiv = document.getElementById('file-results');
            resultsDiv.innerHTML = '<p>Testing file access...</p>';
            
            let results = '';
            for (const file of files) {
                try {
                    const response = await fetch(file, { method: 'HEAD' });
                    const size = response.headers.get('content-length');
                    const status = response.ok ? '✅' : '❌';
                    results += `<div class="file-test">${status} ${file} (${size ? size + ' bytes' : 'unknown size'})</div>`;
                } catch (error) {
                    results += `<div class="file-test">❌ ${file} - Error: ${error.message}</div>`;
                }
            }
            resultsDiv.innerHTML = results;
        }

        async function testEngine() {
            const resultsDiv = document.getElementById('engine-results');
            resultsDiv.innerHTML = '<p>Testing Godot engine loading...</p>';
            
            try {
                // Test if we can load the engine script
                const script = document.createElement('script');
                script.src = 'stiletto.js';
                
                script.onload = () => {
                    if (typeof Engine !== 'undefined') {
                        resultsDiv.innerHTML = '✅ Engine script loaded successfully<br>✅ Engine class available';
                    } else {
                        resultsDiv.innerHTML = '❌ Engine script loaded but Engine class not found';
                    }
                };
                
                script.onerror = () => {
                    resultsDiv.innerHTML = '❌ Failed to load engine script';
                };
                
                document.head.appendChild(script);
                
            } catch (error) {
                resultsDiv.innerHTML = `❌ Engine test failed: ${error.message}`;
            }
        }
    </script>
</body>
</html>
