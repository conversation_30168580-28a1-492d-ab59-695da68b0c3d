<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browser Compatibility Check</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #2a2a2a;
            color: #e0e0e0;
        }
        .check-item {
            background: #3a3a3a;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007acc;
        }
        .supported { border-left-color: #28a745; }
        .not-supported { border-left-color: #dc3545; }
        .warning { border-left-color: #ffc107; }
        .status { font-weight: bold; }
        .details { margin-top: 10px; font-size: 0.9em; opacity: 0.8; }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>🔍 Browser Compatibility Check</h1>
    <p>This page checks if your browser supports the features needed for Godot web exports.</p>
    
    <button onclick="runChecks()">Run Compatibility Check</button>
    <button onclick="testGameLoad()">Test Game Loading</button>
    
    <div id="results"></div>

    <script>
        function runChecks() {
            const results = document.getElementById('results');
            results.innerHTML = '<h2>Checking browser compatibility...</h2>';
            
            const checks = [
                {
                    name: 'WebAssembly Support',
                    test: () => typeof WebAssembly === 'object',
                    required: true,
                    details: 'Required for Godot engine to run'
                },
                {
                    name: 'Fetch API Support',
                    test: () => typeof fetch === 'function',
                    required: true,
                    details: 'Required for loading game files'
                },
                {
                    name: 'Canvas Support',
                    test: () => {
                        const canvas = document.createElement('canvas');
                        return !!(canvas.getContext && canvas.getContext('2d'));
                    },
                    required: true,
                    details: 'Required for game rendering'
                },
                {
                    name: 'WebGL Support',
                    test: () => {
                        const canvas = document.createElement('canvas');
                        return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
                    },
                    required: true,
                    details: 'Required for 3D graphics'
                },
                {
                    name: 'Audio Context Support',
                    test: () => typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined',
                    required: false,
                    details: 'Required for audio playback'
                },
                {
                    name: 'SharedArrayBuffer Support',
                    test: () => typeof SharedArrayBuffer !== 'undefined',
                    required: false,
                    details: 'Required for threading (disabled in compatible mode)'
                },
                {
                    name: 'Cross-Origin Isolation',
                    test: () => crossOriginIsolated === true,
                    required: false,
                    details: 'Required for SharedArrayBuffer (disabled in compatible mode)'
                },
                {
                    name: 'Secure Context',
                    test: () => window.isSecureContext,
                    required: false,
                    details: 'HTTPS or localhost - some features require this'
                }
            ];
            
            let html = '<h2>Compatibility Results</h2>';
            let allRequired = true;
            
            checks.forEach(check => {
                const supported = check.test();
                const className = supported ? 'supported' : (check.required ? 'not-supported' : 'warning');
                const status = supported ? '✅ Supported' : (check.required ? '❌ Not Supported' : '⚠️ Not Available');
                
                if (check.required && !supported) {
                    allRequired = false;
                }
                
                html += `
                    <div class="check-item ${className}">
                        <div class="status">${check.name}: ${status}</div>
                        <div class="details">${check.details}</div>
                    </div>
                `;
            });
            
            if (allRequired) {
                html += `
                    <div class="check-item supported">
                        <div class="status">🎉 Your browser should support Godot web exports!</div>
                        <div class="details">All required features are available. You can try loading the game.</div>
                    </div>
                `;
            } else {
                html += `
                    <div class="check-item not-supported">
                        <div class="status">❌ Your browser may not support Godot web exports</div>
                        <div class="details">Some required features are missing. Try updating your browser or using a different one.</div>
                    </div>
                `;
            }
            
            results.innerHTML = html;
        }
        
        function testGameLoad() {
            const results = document.getElementById('results');
            results.innerHTML = '<h2>Testing game file access...</h2>';
            
            const files = ['stiletto.js', 'stiletto.wasm', 'stiletto.pck'];
            let html = '<h2>File Access Test Results</h2>';
            
            Promise.all(files.map(file => 
                fetch(file, { method: 'HEAD' })
                    .then(response => ({
                        file,
                        success: response.ok,
                        size: response.headers.get('content-length'),
                        status: response.status
                    }))
                    .catch(error => ({
                        file,
                        success: false,
                        error: error.message
                    }))
            )).then(results => {
                results.forEach(result => {
                    const className = result.success ? 'supported' : 'not-supported';
                    const status = result.success ? '✅ Accessible' : '❌ Failed';
                    const details = result.success 
                        ? `Size: ${result.size ? result.size + ' bytes' : 'unknown'}`
                        : `Error: ${result.error || 'HTTP ' + result.status}`;
                    
                    html += `
                        <div class="check-item ${className}">
                            <div class="status">${result.file}: ${status}</div>
                            <div class="details">${details}</div>
                        </div>
                    `;
                });
                
                document.getElementById('results').innerHTML = html;
            });
        }
        
        // Auto-run checks on page load
        window.addEventListener('load', runChecks);
    </script>
</body>
</html>
