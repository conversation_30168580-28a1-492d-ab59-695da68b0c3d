#!/usr/bin/env python3
"""
Simple HTTP server for testing Godot web exports locally.
This avoids CORS issues that occur when opening HTML files directly in the browser.

Usage:
1. Run this script in the build/test directory
2. Open http://localhost:8000/stiletto.html in your browser
"""

import http.server
import socketserver
import webbrowser
import os
import sys
import socket

PORT = 8000

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # Add CORS headers for Godot web exports (without Cross-Origin isolation for better compatibility)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS, HEAD')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Range, Accept-Encoding')
        self.send_header('Access-Control-Expose-Headers', 'Content-Length, Content-Range')

        # Additional headers for better remote compatibility
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')

        # Set proper MIME types for Godot files
        if self.path.endswith('.wasm'):
            self.send_header('Content-Type', 'application/wasm')
        elif self.path.endswith('.pck'):
            self.send_header('Content-Type', 'application/octet-stream')
        elif self.path.endswith('.js'):
            self.send_header('Content-Type', 'application/javascript')

        super().end_headers()

    def do_OPTIONS(self):
        # Handle preflight requests
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS, HEAD')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Range')
        self.end_headers()

    def log_message(self, format, *args):
        # Enhanced logging to help with debugging
        client_ip = self.address_string()
        is_remote = client_ip != "127.0.0.1" and client_ip != "localhost"
        prefix = "🌐 REMOTE" if is_remote else "🏠 LOCAL"
        print(f"{prefix} [{client_ip}] {format % args}")

    def guess_type(self, path):
        # Override MIME type guessing for Godot files
        result = super().guess_type(path)
        if isinstance(result, tuple):
            mimetype, encoding = result
        else:
            mimetype, encoding = result, None

        if path.endswith('.wasm'):
            return 'application/wasm'
        elif path.endswith('.pck'):
            return 'application/octet-stream'
        elif path.endswith('.js'):
            return 'application/javascript'
        return mimetype

def get_local_ip():
    """Get the local IP address for remote access"""
    try:
        # Connect to a remote address to determine local IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            return s.getsockname()[0]
    except Exception:
        return "localhost"

def main():
    # Change to the directory containing this script
    os.chdir(os.path.dirname(os.path.abspath(__file__)))

    local_ip = get_local_ip()

    print(f"Starting server on port {PORT}")
    print(f"Serving files from: {os.getcwd()}")
    print(f"")
    print(f"🖥️  Local Access URLs:")
    print(f"  🔧 Debug Tool: http://localhost:{PORT}/debug_initialization.html")
    print(f"  ⚡ Minimal Test: http://localhost:{PORT}/minimal_test.html")
    print(f"  🎮 Custom Shell: http://localhost:{PORT}/stiletto.html")
    print(f"  🧪 API Compatibility Test: http://localhost:{PORT}/api_compatibility_test.html")
    print(f"  📋 Simple Test: http://localhost:{PORT}/simple_test.html")
    print(f"  ⚙️  Engine API Test: http://localhost:{PORT}/engine_test.html")
    print(f"  📁 File Checker: http://localhost:{PORT}/check_files.html")
    print(f"")
    print(f"📱 Remote Access URLs (for other devices on your network):")
    print(f"  🔧 Debug Tool: http://{local_ip}:{PORT}/debug_initialization.html")
    print(f"  ⚡ Minimal Test: http://{local_ip}:{PORT}/minimal_test.html")
    print(f"  🎮 Custom Shell: http://{local_ip}:{PORT}/stiletto.html")
    print(f"  🧪 API Compatibility Test: http://{local_ip}:{PORT}/api_compatibility_test.html")
    print(f"  📋 Simple Test: http://{local_ip}:{PORT}/simple_test.html")
    print(f"  ⚙️  Engine API Test: http://{local_ip}:{PORT}/engine_test.html")
    print(f"  📁 File Checker: http://{local_ip}:{PORT}/check_files.html")
    print(f"")
    print(f"🚨 RECOMMENDED: Try Minimal Test first for basic functionality!")
    print(f"")
    print("Press Ctrl+C to stop the server")
    
    try:
        with socketserver.TCPServer(("0.0.0.0", PORT), MyHTTPRequestHandler) as httpd:
            # Automatically open the browser
            webbrowser.open(f'http://localhost:{PORT}/stiletto.html')
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\nServer stopped.")
        sys.exit(0)

if __name__ == "__main__":
    main()
